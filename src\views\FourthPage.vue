<template>
  <div class="fourth-page-container">
    <div class="header-container">
      <h1 class="header-text">
        <span class="app-text">应用</span>服务
      </h1>
      <h4 class="small-text">
        Application Services
      </h4>
    </div>
    <div class="service-container">
      <div
        class="service-item"
        :class="{ active: selectedService === 'consult' }"
        @click="selectService('consult')"
      >
        <img src="../assets/AppService/consult.svg" width="22px" height="22px" alt="咨询服务" />
        <span class="service-text">咨询服务</span>
      </div>
      <div
        class="service-item"
        :class="{ active: selectedService === 'technology' }"
        @click="selectService('technology')"
      >
        <img src="../assets/AppService/technology.svg" width="22px" height="22px" alt="技术服务" />
        <span class="service-text">技术服务</span>
      </div>
      <div
        class="service-item"
        :class="{ active: selectedService === 'model' }"
        @click="selectService('model')"
      >
        <img src="../assets/AppService/model.svg" width="22px" height="22px" alt="模型服务" />
        <span class="service-text">模型服务</span>
      </div>
      <div
        class="service-item"
        :class="{ active: selectedService === 'financial' }"
        @click="selectService('financial')"
      >
        <img src="../assets/AppService/financial.svg" width="22px" height="22px" alt="金融服务" />
        <span class="service-text">金融服务</span>
      </div>
    </div>
    <div class="service-sub-item">
      <div v-for="card in currentServiceCards" :key="card.id" class="service-card">
        <div class="card-sub">
          <div class="card-header">
            <img :src="card.icon" width="60px" height="60px" :alt="card.title" class="card-icon">
            <div class="card-title">
              <h3>{{ card.title }}</h3>
            </div>
            <div class="card-description">
              <p>{{ card.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="totalPages > 1" class="pagination-container">
      <img
        src="../assets/left.svg"
        width="40px"
        height="40px"
        class="page-turning"
        :class="{ disabled: !canGoPrevious }"
        @click="goToPreviousPage"
      />
      <img
        src="../assets/right.svg"
        width="40px"
        height="40px"
        class="page-turning-right"
        :class="{ disabled: !canGoNext }"
        @click="goToNextPage"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 服务卡片数据类型
interface ServiceCard {
  id: string
  title: string
  icon: string
  description: string
}

// 服务数据
const serviceData: Record<string, ServiceCard[]> = {
  consult: [
    {
      id: 'consult-1',
      title: '业务数字化咨询',
      icon: '/src/assets/AppService/Business_figures_mr.svg',
      description: '将传统业务进行转型，以数据为驱动提升业务效率和改变业务模式。'
    },
    {
      id: 'consult-2',
      title: '商业模式咨询',
      icon: '/src/assets/AppService/business_model_mr.svg',
      description: '采用的策略、结构、流程、盈利方式以及与其他利益相关者的关系等方面所构成的整体框架。'
    },
    {
      id: 'consult-3',
      title: '技术方案咨询',
      icon: '/src/assets/AppService/technical_proposal.svg',
      description: '从企业业务数字化到数字化业务的IT、DT，OT信息技术和数字技术，以及运营技术的规划。从工业化信息化时代转型进化升级到未来数字经济数字科技时代，AI（人工智能）、Blokechina（区块链）、Cloud（云服务）、Digitization（数字化）、Iot（物联网）协同企业业务发展战略同步科技战略开发引导企业形成具有优势的新质生产力和人工智能的应用'
    },
    {
      id: 'consult-4',
      title: '技术指标咨询',
      icon: '/src/assets/AppService/performance_indicator_mr.svg',
      description: '数字化转型的综合进度，内容、成果、成本，效益指标成熟度建设'
    },
    {
      id: 'consult-5',
      title: '投资管理咨询',
      icon: '/src/assets/AppService/investment_management.svg',
      description: '企业服务、财务、金融、商业模式、陪跑、孵化一站式数字化服务'
    }
  ],
  technology: [
    {
      id: 'tech-1',
      title: 'PLM',
      icon: '/src/assets/AppService/PLM.svg',
      description: '提供前端、后端、移动端全栈开发服务，采用最新技术栈，确保系统稳定性和可扩展性。'
    },
    {
      id: 'tech-2',
      title: 'ERP',
      icon: '/src/assets/AppService/ERP.svg',
      description: '设计高可用、高性能的系统架构，包括微服务架构、云原生架构和分布式系统设计。'
    },
    {
      id: 'tech-3',
      title: 'SCM',
      icon: '/src/assets/AppService/SCM.svg',
      description: '提供技术选型建议、代码审查、性能优化和技术培训等专业技术咨询服务。'
    },
    {
      id: 'tech-4',
      title: 'WMS',
      icon: '/src/assets/AppService/WMS.svg',
      description: '提供技术选型建议、代码审查、性能优化和技术培训等专业技术咨询服务。'
    },
    {
      id: 'tech-5',
      title: 'MES',
      icon: '/src/assets/AppService/MES.svg',
      description: '提供技术选型建议、代码审查、性能优化和技术培训等专业技术咨询服务。'
    },
    {
      id: 'tech-6',
      title: 'OA',
      icon: '/src/assets/AppService/OA.svg',
      description: '提供技术选型建议、代码审查、性能优化和技术培训等专业技术咨询服务。'
    },
    {
      id: 'tech-7',
      title: 'APS',
      icon: '/src/assets/AppService/APS.svg',
      description: '提供技术选型建议、代码审查、性能优化和技术培训等专业技术咨询服务。'
    }
  ],
  model: [
    {
      id: 'model-1',
      title: '数字建模',
      icon: '/src/assets/AppService/digital_modeling.svg',
      description: '根据订单业务数字化，供应链数字化和产业链数字化的元数据和数据源根据管理决策和经营决策建立动态数据模型'
    },
    {
      id: 'model-2',
      title: '数字影子',
      icon: '/src/assets/AppService/digital_shadow.svg',
      description: '根据业务动态数据血缘以及数据关系提炼数据要素设置映射与实体业务同步的影子数据，提供治理和应用的有效数值参数设置'
    }
  ],
  financial: [
    {
      id: 'financial-1',
      title: '消费金融',
      icon: '/src/assets/AppService/consume_finance.svg',
      description: '各大银行综合个人消费贷'
    },
    {
      id: 'financial-2',
      title: '产业金融',
      icon: '/src/assets/AppService/industrial_finance.svg',
      description: 'CIC、CVC、供应链金融'
    },
    {
      id: 'financial-3',
      title: '风险金融',
      icon: '/src/assets/AppService/risk_finance.svg',
      description: 'VC、PE、IPO'
    }
  ]
}

// 选中的服务类型
const selectedService = ref<string>('consult') // 默认选中咨询服务

// 当前页码
const currentPage = ref<number>(0)

// 每页显示的卡片数量
const cardsPerPage = 4

// 当前选中服务的所有卡片数据
const allServiceCards = computed(() => {
  return serviceData[selectedService.value] || []
})

// 当前页显示的卡片数据
const currentServiceCards = computed(() => {
  const startIndex = currentPage.value * cardsPerPage
  const endIndex = startIndex + cardsPerPage
  return allServiceCards.value.slice(startIndex, endIndex)
})

// 总页数
const totalPages = computed(() => {
  return Math.ceil(allServiceCards.value.length / cardsPerPage)
})

// 是否可以向前翻页
const canGoPrevious = computed(() => {
  return currentPage.value > 0
})

// 是否可以向后翻页
const canGoNext = computed(() => {
  return currentPage.value < totalPages.value - 1
})

// 选择服务的方法
const selectService = (serviceType: string) => {
  selectedService.value = serviceType
  currentPage.value = 0
}

// 向前翻页
const goToPreviousPage = () => {
  if (canGoPrevious.value) {
    currentPage.value--
  }
}

// 向后翻页
const goToNextPage = () => {
  if (canGoNext.value) {
    currentPage.value++
  }
}
</script>
<style scoped>
.fourth-page-container {
  background-image: url("../assets/fourth_background.png");
  width: calc(100% - 48px);
  height: 928px;
  margin: 0px 24px 0px 24px;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* margin-bottom: 120px; */
}

.header-text {
  padding-top: 120px;
  margin-bottom: 0px;
  color: var(--text-white, #FFF);
  font-family: "江城斜黑体", sans-serif;
  font-size: 48px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  letter-spacing: 4.8px;
}

.app-text {
  color: #2f7dfb;
}

.small-text {
  margin-top: 0;
  color: var(--text-4, #C9CDD4);
  text-align: center;
  font-family: JiangChengXieHei;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 1.6px;
  margin-bottom: 80px;
}

.service-text {
  color: var(--text-white, #FFF);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.service-container {
  display: flex;
  align-items: center;
  gap: 32px;
  margin-bottom: 40px;
}

.service-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 157px;
  height: 48px;
  margin: 10px 0;
  padding: 0 24px;
  border-radius: 10px;
  box-sizing: border-box;
}

.service-item:hover {
  background-color: #2d3441;
  cursor: pointer;
}

.service-item.active {
  background-color: #424854;
}

.service-item.active .service-text {
  color: #2f7dfb;
}

.service-item.active img {
  filter: brightness(0) saturate(100%) invert(42%) sepia(95%) saturate(1234%) hue-rotate(207deg) brightness(98%) contrast(96%);
}

.service-sub-item {
  display: flex;
  gap: 24px;
  width: fit-content;
  height: auto;
  margin-bottom: 80px;
}

.service-card {
  width: 342px;
  height: 320px;
  background-color: white;
  border-radius: 20px;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-sizing: border-box;
}

.card-sub {
  width: 322px;
  height: 300px;
  display: flex;
  flex-direction: column;
  background-image: url("../assets/service_item_background.png");
  border-radius: 20px;
}

.card-sub:hover {
  background-image: url("../assets/service_item_background_xt.png");
}

.card-sub:hover .card-icon {
  filter: brightness(0) saturate(100%) invert(42%) sepia(95%) saturate(1234%) hue-rotate(207deg) brightness(98%) contrast(96%);
}

.card-header {
  padding: 24px 24px;
  height: 252px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-title {
  width: 274px;
  height: 28px;
  margin-bottom: -60px;
}

.card-title h3 {
  color: var(--text-1, #1D2129);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 20px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
}

.card-description {
  width: 274px;
  padding-bottom: 0px;
}

.card-description p {
  overflow: hidden;
  color: var(--text-3, #86909C);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 150% */
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}

.pagination-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40px;
}

.page-turning,
.page-turning-right {
  cursor: pointer;
  transition: opacity 0.3s ease, transform 0.2s ease;
}

.page-turning:hover,
.page-turning-right:hover {
  transform: scale(1.1);
}

.page-turning.disabled,
.page-turning-right.disabled {
  opacity: 0.3;
  cursor: not-allowed;
  pointer-events: none;
}
</style>