<template>
  <div class="fourth-page-container">
    <div class="header-container">
      <h1 class="header-text">
        <span class="app-text">应用</span>服务
      </h1>
      <h4 class="small-text">
        Application Services
      </h4>
    </div>
    <div class="service-container">
      <div
        class="service-item"
        :class="{ active: selectedService === 'consult' }"
        @click="selectService('consult')"
      >
        <img src="../assets/AppService/consult.svg" width="22px" height="22px" alt="咨询服务" />
        <span class="service-text">咨询服务</span>
      </div>
      <div
        class="service-item"
        :class="{ active: selectedService === 'technology' }"
        @click="selectService('technology')"
      >
        <img src="../assets/AppService/technology.svg" width="22px" height="22px" alt="技术服务" />
        <span class="service-text">技术服务</span>
      </div>
      <div
        class="service-item"
        :class="{ active: selectedService === 'model' }"
        @click="selectService('model')"
      >
        <img src="../assets/AppService/model.svg" width="22px" height="22px" alt="模型服务" />
        <span class="service-text">模型服务</span>
      </div>
      <div
        class="service-item"
        :class="{ active: selectedService === 'financial' }"
        @click="selectService('financial')"
      >
        <img src="../assets/AppService/financial.svg" width="22px" height="22px" alt="金融服务" />
        <span class="service-text">金融服务</span>
      </div>
    </div>
    <div class="service-sub-item">
      <div v-for="card in currentServiceCards" :key="card.id" class="service-card">
        <div class="card-sub">
          <div class="card-header">
            <img :src="card.icon" width="60px" height="60px" :alt="card.title" class="card-icon">
            <div class="card-title">
              <h3>{{ card.title }}</h3>
            </div>
            <div class="card-description">
              <p>{{ card.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 服务卡片数据类型
interface ServiceCard {
  id: string
  title: string
  icon: string
  description: string
}

// 服务数据
const serviceData: Record<string, ServiceCard[]> = {
  consult: [
    {
      id: 'consult-1',
      title: '业务数字化咨询',
      icon: '/src/assets/AppService/Business_figures_mr.svg',
      description: '提供专业的数字化转型咨询服务，帮助企业制定数字化战略，优化业务流程，提升运营效率。'
    },
    {
      id: 'consult-2',
      title: '战略规划咨询',
      icon: '../assets/AppService/strategy_icon.svg',
      description: '协助企业制定长期发展战略，分析市场趋势，识别商业机会，制定可执行的战略规划。'
    }
  ],
  technology: [
    {
      id: 'tech-1',
      title: '全栈开发服务',
      icon: '../assets/AppService/fullstack_icon.svg',
      description: '提供前端、后端、移动端全栈开发服务，采用最新技术栈，确保系统稳定性和可扩展性。'
    },
    {
      id: 'tech-2',
      title: '系统架构设计',
      icon: '../assets/AppService/architecture_icon.svg',
      description: '设计高可用、高性能的系统架构，包括微服务架构、云原生架构和分布式系统设计。'
    },
    {
      id: 'tech-3',
      title: '技术咨询服务',
      icon: '../assets/AppService/tech_consulting_icon.svg',
      description: '提供技术选型建议、代码审查、性能优化和技术培训等专业技术咨询服务。'
    },
    {
      id: 'tech-3',
      title: '技术咨询服务',
      icon: '../assets/AppService/tech_consulting_icon.svg',
      description: '提供技术选型建议、代码审查、性能优化和技术培训等专业技术咨询服务。'
    }
  ],
  model: [
    {
      id: 'model-1',
      title: 'AI模型开发',
      icon: '../assets/AppService/ai_model_icon.svg',
      description: '开发定制化AI模型，包括机器学习、深度学习模型，满足特定业务需求。'
    },
    {
      id: 'model-2',
      title: '自然语言处理',
      icon: '../assets/AppService/nlp_icon.svg',
      description: '提供文本分析、语义理解、智能问答等自然语言处理解决方案。'
    }
  ],
  financial: [
    {
      id: 'financial-1',
      title: '支付系统开发',
      icon: '../assets/AppService/payment_icon.svg',
      description: '开发安全可靠的支付系统，支持多种支付方式，确保交易安全和用户体验。'
    },
    {
      id: 'financial-2',
      title: '风控模型设计',
      icon: '../assets/AppService/risk_icon.svg',
      description: '设计智能风控模型，实时监控交易风险，保障金融业务安全。'
    },
    {
      id: 'financial-3',
      title: '区块链应用',
      icon: '../assets/AppService/blockchain_icon.svg',
      description: '开发基于区块链的金融应用，包括数字货币、智能合约和去中心化金融服务。'
    }
  ]
}

// 选中的服务类型
const selectedService = ref<string>('consult') // 默认选中咨询服务

// 当前选中服务的卡片数据
const currentServiceCards = computed(() => {
  return serviceData[selectedService.value] || []
})

// 选择服务的方法
const selectService = (serviceType: string) => {
  selectedService.value = serviceType
}
</script>
<style scoped>
.fourth-page-container {
  background-image: url("../assets/fourth_background.png");
  width: calc(100% - 48px);
  height: 928px;
  margin: 0px 24px 0px 24px;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header-text {
  padding-top: 120px;
  margin-bottom: 0px;
  color: var(--text-white, #FFF);
  font-family: "江城斜黑体", sans-serif;
  font-size: 48px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  letter-spacing: 4.8px;
}

.app-text {
  color: #2f7dfb;
}

.small-text {
  margin-top: 0;
  color: var(--text-4, #C9CDD4);
  text-align: center;
  font-family: JiangChengXieHei;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 1.6px;
  margin-bottom: 80px;
}

.service-text {
  color: var(--text-white, #FFF);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.service-container {
  display: flex;
  align-items: center;
  gap: 32px;
  margin-bottom: 40px;
}

.service-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 157px;
  height: 48px;
  margin: 10px 0;
  padding: 0 24px;
  border-radius: 10px;
  box-sizing: border-box;
}

.service-item:hover {
  background-color: #2d3441;
  cursor: pointer;
}

.service-item.active {
  background-color: #424854;
}

.service-item.active .service-text {
  color: #2f7dfb;
}

.service-item.active img {
  filter: brightness(0) saturate(100%) invert(42%) sepia(95%) saturate(1234%) hue-rotate(207deg) brightness(98%) contrast(96%);
}

.service-sub-item {
  display: flex;
  gap: 24px;
  width: fit-content;
  height: auto;
}

.service-card {
  width: 342px;
  height: 320px;
  background-color: white;
  border-radius: 20px;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-sizing: border-box;
}

.card-sub {
  width: 322px;
  height: 300px;
  display: flex;
  flex-direction: column;
  background-image: url("../assets/service_item_background.png");
  border-radius: 20px;
}

.card-sub:hover {
  background-image: url("../assets/service_item_background_xt.png");
}

.card-header {
  padding: 24px 24px;
  height: 252px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-title {
  width: 274px;
  height: 28px;
  margin-bottom: 5px;
}

.card-title h3 {
  color: var(--text-1, #1D2129);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 20px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
}

.card-description {
  width: 274px;
  padding-bottom: 0px;
}

.card-description p {
  overflow: hidden;
  color: var(--text-3, #86909C);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 150% */
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}
</style>