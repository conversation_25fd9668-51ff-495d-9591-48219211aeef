<template>
  <div class="page-container">
    <!-- 第一页 - 首页 -->
    <div class="home-container">
      <!-- 顶部导航栏 -->
      <div class="topbar">
        <img src="../assets/logo.svg" alt="logo" class="logo" />
        <div class="nav">
          <span class="nav-item active">首页</span>
          <span class="nav-item">关于我们</span>
          <span class="nav-item">应用服务</span>
          <span class="nav-item">服务体验</span>
          <span class="nav-item">订阅</span>
        </div>
        <div class="topbutton">
          <el-button class="login-btn">登录</el-button>
          <el-button type="primary" class="register-btn">注册</el-button>
        </div>
      </div>

      <div class="main-content">
        <div class="center-content">
          <img src="../assets/copywriter.png" alt="Spacetime&AIOS" class="main-logo" />
          <el-button type="primary" size="large" class="experience-btn">
            立即体验
          </el-button>
        </div>
      </div>
    </div>
    <second-page/>
    <third-page/>
    <fourth-page/>
  </div>
</template>

<script setup lang="ts">
import { ElButton } from 'element-plus';
import { onMounted } from 'vue'
import SecondPage from './SecondPage.vue';
import ThirdPage from './ThirdPage.vue';
import FourthPage from './FourthPage.vue';
onMounted(() => {
  
})
</script>

<style>
/* 全局隐藏滚动条 */
html, body {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

html::-webkit-scrollbar,
body::-webkit-scrollbar {
  display: none;
}
</style>

<style scoped>
.page-container {
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  scroll-behavior: smooth;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.page-container::-webkit-scrollbar {
  display: none;
}

.home-container {
  min-height: 100vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  margin: 10px 24px 0px 24px;
  border-radius: 20px 20px 0 0;
  overflow: hidden;
}

.topbar {
  /* padding: 0px 24px; */
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 10;
  height: 68px;
  margin-top: -5px;
}

.logo {
  height: 32px;
  width: auto;
}

.nav {
  display: flex;
  gap: 40px;
  align-items: center;
}

.nav-item {
  color: break;
  font-size: 16px;
  font-weight: 400;
  cursor: pointer;
  transition: color 0.3s ease;
  padding: 8px 0;
}

.nav-item:hover {
  color: #5B9BD5;
}

.nav-item.active {
  color: #5B9BD5;
  font-weight: 500;
}

.topbutton {
  display: flex;
  gap: 12px;
  align-items: center;
}

.login-btn {
  width: 72px;
  height: 36px;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: black;
}

.login-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #5B9BD5;
}

.register-btn {
  width: 72px;
  height: 36px;
  background: #1e69e2;
  border: none;

}

.main-content {
  border-radius: 10px;
  background-image: url('../assets/banner.png');
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 12vh);
  padding: 0 24px;
}

.center-content {
  text-align: center;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
}

.main-logo {
  max-width: 600px;
  width: 100%;
  height: auto;
  margin-bottom: 30px;
  margin-top: 180px;
}

.subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 40px;
  letter-spacing: 2px;
}

.experience-btn {
  font-size: 16px;
  padding: 16px 40px;
  background: #1e69e2; /* 按钮背景色 */
  border: none;
  border-radius: 12px;
  font-family: "江城斜黑体", sans-serif;
  font-weight: 900;
  min-width: 154px;
  width: 154px;
  height: 48px;
  margin-top: 120px;
}

.experience-btn::before {
  background: linear-gradient(180deg, #FFF 0%, rgba(255, 255, 255, 0.40) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.experience-btn:hover {
  background: #7BB3E0;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(20, 131, 235, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home-container {
    margin: 5px 10px 0px 10px;
    border-radius: 15px 15px 0 0;
  }

  .topbar {
    padding: 8px 20px;
    flex-direction: column;
    gap: 10px;
    height: auto;
    min-height: 60px;
  }

  .nav {
    gap: 20px;
  }

  .nav-item {
    font-size: 14px;
  }

  .main-content {
    padding: 0 20px;
    min-height: calc(100vh - 60px);
  }

  .main-logo {
    max-width: 400px;
  }

  .experience-btn {
    padding: 10px 50px;
    font-size: 14px;
    margin-top: 40px;
  }
}

@font-face {
  font-family: "江城斜黑体";
  src: url("../assets/typeface/JiangXieHei500W.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "江城斜黑体";
  src: url("../assets/typeface/JiangXieHei900W.ttf") format("truetype");
  font-weight: 900;
  font-style: normal;
}
</style>