import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import IndexView from './views/index.vue'
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';

// 添加全局CSS来隐藏滚动条
const globalStyle = document.createElement('style');
globalStyle.textContent = `
  /* 隐藏所有滚动条 */
  * {
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }

  *::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
  }

  html, body {
    overflow-x: hidden !important;
  }
`;
document.head.appendChild(globalStyle);

// 创建路由配置
const routes = [
  {
    path: '/',
    name: 'Home',
    component: IndexView
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 创建应用实例并使用路由
const app = createApp(App)
app.use(ElementPlus)
app.use(router)
app.mount('#app')
