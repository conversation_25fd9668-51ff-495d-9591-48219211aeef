<template>
  <div class="fifth-page-container">
    <div class="header-container">
      <h1 class="header-text">
        <span class="service-text">服务</span>体验
      </h1>
      <h4 class="small-text">
        Service Experience
      </h4>
    </div>
    <div class="service-experience">
      <div class="service-item">
        <img src="../assets/ServiceExperience/house.svg" width="22px" height="22px"/>
        <span class="item-text">住</span>
      </div>
      <div class="service-item">
        <img src="../assets/ServiceExperience/eat.svg" width="22px" height="22px"/>
        <span class="item-text">食</span>
      </div>
      <div class="service-item">
        <img src="../assets/ServiceExperience/clothing.svg" width="22px" height="22px"/>
        <span class="item-text">衣</span>
      </div>
      <div class="service-item">
        <img src="../assets/ServiceExperience/trip.svg" width="22px" height="22px"/>
        <span class="item-text">行</span>
      </div>
    </div>
    <div class="service-sub-item">
      <div class="left-container">
        <div class="left-item">
          <img src="../assets/ServiceExperience/left_house.svg" width="36px" height="36px"/>
          <span>楼盘云-SAPP</span>
          <p>将传统业务进行转型，以数据为驱动提升业务效率和改变业务模式</p>
        </div>
        <div class="left-item">
          <img src="../assets/ServiceExperience/design_cloud.svg" width="36px" height="36px"/>
          <span>设计云</span>
          <p>将传统业务进行转型，以数据为驱动提升业务效率和改变业务模式</p>
        </div>
        <div class="left-item">
          <img src="../assets/ServiceExperience/RD_cloud.svg" width="36px" height="36px"/>
          <span>研发云</span>
          <p>将传统业务进行转型，以数据为驱动提升业务效率和改变业务模式</p>
        </div>
      </div>
      <div class="right-item">
        <img src="../assets/ServiceExperience/house_main_picture.png" width="1084px" height="692px"/>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

</script>
<style scoped>
.fifth-page-container {
  width: calc(100% - 48px);
  margin: 0px 24px 0px 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header-text {
  padding-top: 120px;
  margin-bottom: 0px;
  color: var(--text-1, #1D2129);
  font-family: "江城斜黑体", sans-serif;
  font-size: 48px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  letter-spacing: 4.8px;
}

.service-text {
  color: #00d8c1;
}

.small-text {
  margin-top: 0;
  color: var(--text-4, #C9CDD4);
  text-align: center;
  font-family: JiangChengXieHei;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 1.6px;
  margin-bottom: 80px;
}

.service-experience {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 31px;
  margin-bottom: 40px;
}

.item-text {
  color: var(--text-4, #C9CDD4);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.service-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 98px;
  height: 48px;
  margin: 10px 0;
  padding: 0 24px;
  border-radius: 10px;
  box-sizing: border-box;
}

.service-sub-item {
  width: 1440px;
  height: 692px;
  display: flex;
  gap: 24px;
}

.left-container {
  display: flex;
  flex-direction: column;
  width: 332px;
  flex-shrink: 0;
  margin-right: 24px;
}

.left-item {
  height: 160px;
  width: 332px;
  border: 1px solid var(--border-2, #E5E6EB);
  display: flex;
  flex-direction: column;
  border-radius: 20px;
  background-color: #ffffff;
  margin-bottom: 24px;
  padding: 24px 0 0 24px;
}

.left-item:last-child {
  margin-bottom: 0;
}

.left-item > span {
  color: var(--text-1, #1D2129);
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 20px;
  font-style: normal;
  font-weight: 900;
  line-height: normal;
  margin-bottom: 10px;
}

.left-item > p {
  margin-top: 0px;
  overflow: hidden;
  color: var(--text-3, #86909C);
  text-overflow: ellipsis;
  font-family: "Alibaba PuHuiTi 2.0";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  width: 268px;
  height: 48px;
}

.right-item {
  flex-shrink: 0;
}

.right-item > img {
  border-radius: 20px;
}

</style>